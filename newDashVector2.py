# newDashVector.py
import json
from flask import Flask, request
import dashscope
from dashvector import Client, Doc
import uuid
from threading import Thread
import pymysql
from dbutils.pooled_db import PooledDB
from datetime import datetime
from http import HTTPStatus
from openai import OpenAI
# 添加日志模块
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# 阿里dashscope相关配置
dashscope_api_key = 'sk-6f882d82aa9645c1a799e95c165a86c6'
dashvector_api_key = 'sk-UeyJDmZHk6HD5rPfRQv6PZ6301q0Q1C1C091F8A9A11EEBF5562E118F409F5'
dashvector_endpoint = 'vrs-cn-0mm44scjt000bq.dashvector.cn-beijing.aliyuncs.com'

# 创建数据库连接池
def create_conn_pool():
    logger.info("创建数据库连接池")
    pool = PooledDB(
        creator=pymysql,  # 使用pymysql连接数据库
        maxconnections=10,  # 连接池允许的最大连接数
        mincached=2,  # 初始化时，连接池中至少创建的空闲的连接数
        maxcached=5,  # 连接池中最多闲置的连接数
        maxshared=3,  # 连接池中最多共享的连接数
        blocking=True,  # 连接池中如果没有可用连接后，是否阻塞等待
        maxusage=None,  # 一个连接最多被重复使用的次数
        setsession=[],  # 开始会话前执行的命令列表
        ping=0,  # ping MySQL服务端，检查服务是否可用
        #host='innerdev001.mysql.rds.aliyuncs.com',
        host='hzshkjdev001.mysql.rds.aliyuncs.com',
        port=3306,
        user='root',
        password='peilian123a!',
        database='cool-admin',
        charset='utf8mb4'
    )
    logger.info("数据库连接池创建成功")
    return pool

# 获取图片路径，将图片转为向量，并向量存储到向量数据库中
@app.route("/upload", methods=["POST"])
def upload():
    logger.info("开始上传图片或文字")
    
    # 解析传参
    param = request.json
    logger.info(f"接收到参数: {param}")
    param_url = ''
    param_image_name = ''
    param_input_value = ''
    type_value = param['type']
    if 'url' in param:
        param_url = param['url']
    if 'imageName' in param:
        param_image_name = param['imageName']
    if 'inputValue' in param:
        param_input_value = param['inputValue']
    
    logger.info(f"准备启动线程处理上传任务，参数: url={param_url}, name={param_image_name}, value={param_input_value}, type={type_value}")
    t = Thread(target=do_upload, args=(param_url, param_image_name, param_input_value, type_value))
    t.start()

    return 'ok'

# 获取图片路径，将图片转为向量，并向量存储到向量数据库中
def do_upload(param_url, param_image_name, param_input_value, type_value):
    logger.info(f"开始执行上传任务: url={param_url}, name={param_image_name}, value={param_input_value}, type={type_value}")
    random_uuid = uuid.uuid4()
    if type_value == 0:
        # 查找相关数据
        count_number = count_file(param_url)
        if count_number > 0:
            logger.info("文件已存在，跳过处理")
            return
        # 将相关数据存储到数据库
        insert_file_v2(param_url, param_image_name, random_uuid, 0)
    else:
        # 将相关数据存储到数据库
        insert_file(param_input_value, random_uuid, 0)

    # 将内容存储到向量数据库
    update_embeddings(param_url, param_image_name, param_input_value, type_value, random_uuid)
    logger.info("向量数据库存储结束")

# 查询数据是否存在
def count_file(param_url):
    try:
        logger.info(f"查询文件是否存在: {param_url}")
        sql = "SELECT count(1) FROM tb_file_new WHERE file_url = %s"
        result = select_data(sql, param_url)
        logger.info(f"查询结果: {result[0][0]}")
        return result[0][0]
    except Exception as e:
        logger.error(f"查询数据时发生错误: {e}")
        return 0

# 将数据插入到数据库
def insert_file(input_value, random_uuid, do_number):
    try:
        logger.info(f"插入文本数据到数据库: value={input_value}, uuid={random_uuid}")
        sql = "INSERT INTO tb_file_new (input_value,random_uuid,creat_time,status,do_number) VALUES (%s,%s,%s,%s,%s)"
        val = (input_value, random_uuid, datetime.now(), '1', do_number)
        insert_update_delete(sql, val)
        logger.info("文本数据插入成功")
    except Exception as e:
        logger.error(f"插入文本数据时发生错误: {e}")

# 将数据插入到数据库
def insert_file_v2(param_url, param_video_name, random_uuid, do_number):
    try:
        logger.info(f"插入文件数据到数据库: url={param_url}, name={param_video_name}, uuid={random_uuid}")
        sql = "INSERT INTO tb_file_new (file_url,file_name,random_uuid,creat_time,status,do_number) VALUES (%s,%s,%s,%s,%s,%s)"
        val = (param_url, param_video_name, random_uuid, datetime.now(), '1', do_number)
        insert_update_delete(sql, val)
        logger.info("文件数据插入成功")
    except Exception as e:
        logger.error(f"插入文件数据时发生错误: {e}")

# 新增/更新/删除数据
def insert_update_delete(sql, values):
    logger.info("执行数据库写入操作")
    with pool.connection() as conn:
        with conn.cursor() as cursor:
            cursor.execute(sql, values)
            conn.commit()
            row_count = cursor.rowcount
            logger.info(f"数据库操作完成，影响行数: {row_count}")
            return row_count

# 查询数据
def select_data(sql, values):
    logger.info("执行数据库查询操作")
    with pool.connection() as conn:
        with conn.cursor() as cursor:
            cursor.execute(sql, values)
            result = cursor.fetchall()
            logger.info(f"查询完成，返回 {len(result)} 条记录")
            return result

# 插入问题记录到数据库
def insert_question_record(question_content, question_type, answer_content, 
                          question_token, answer_token, total_token, 
                          user_id, knowledge_id, question_url):
    """
    将查询记录插入到 question_record 表中，并返回主键ID
    """
    try:
        logger.info(f"插入问题记录: {question_content}")
        sql = """INSERT INTO question_record 
                 (questionContent, questionType, answerContent, questionToken, 
                  answerToken, totalToken, userId, knowledgeId, questionUrl) 
                 VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)"""
        val = (question_content, question_type, answer_content, 
               question_token, answer_token, total_token, 
               user_id, knowledge_id, question_url)
        
        with pool.connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(sql, val)
                conn.commit()
                record_id = cursor.lastrowid  # 获取插入记录的主键ID
                logger.info(f"问题记录插入成功，ID: {record_id}")
                return record_id
    except Exception as e:
        logger.error(f"插入问题记录时发生错误: {e}")
        return None

# 扣除用户token
def deduct_user_token(user_id, token_count):
    """
    根据用户ID扣除相应的token数量
    :param user_id: 用户ID
    :param token_count: 需要扣除的token数量
    :return: 扣除结果 True/False
    """
    try:
        logger.info(f"开始为用户 {user_id} 扣除 {token_count} 个token")
        
        # 查询用户当前token余额
        select_sql = "SELECT token FROM base_sys_user WHERE id = %s"
        result = select_data(select_sql, (user_id,))
        
        if not result:
            logger.warning(f"未找到用户 {user_id}")
            return False
            
        current_token = result[0][0] if result[0][0] else 0
        logger.info(f"用户 {user_id} 当前token余额: {current_token}")
        
        # 检查token余额是否足够
        #if current_token < token_count:
        #    logger.warning(f"用户 {user_id} token余额不足，当前余额: {current_token}，需要扣除: {token_count}")
        #    return False
            
        # 扣除token
        new_token = current_token - token_count
        update_sql = "UPDATE base_sys_user SET token = %s WHERE id = %s"
        val = (new_token, user_id)
        
        with pool.connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(update_sql, val)
                conn.commit()
                affected_rows = cursor.rowcount
                if affected_rows > 0:
                    logger.info(f"成功为用户 {user_id} 扣除 {token_count} 个token，剩余: {new_token}")
                    return True
                else:
                    logger.error(f"更新用户 {user_id} token失败")
                    return False
                    
    except Exception as e:
        logger.error(f"扣除用户 {user_id} token时发生错误: {e}")
        return False

def update_embeddings(file_url, file_name, input_value, type_value, random_uuid):
    logger.info(f"开始向量化处理: url={file_url}, name={file_name}, type={type_value}")
    dashscope.api_key = dashscope_api_key

    # 初始化 dashvector client
    client = Client(
        api_key=dashvector_api_key,
        endpoint=dashvector_endpoint
    )

    vectors = generate_embeddings(file_url, input_value, type_value)
    if not vectors:
        logger.error("数据向量化失败")
        return

    if type_value == 0:
        # 连接集合
        collection = client.get('test')
        logger.info("向图片向量库插入数据")
        ret = collection.insert(
            [
                Doc(
                    id=str(random_uuid),
                    vector=vectors,
                    fields={'title': file_name,
                            'url': file_url,
                            'value': input_value,
                            'id': '',
                            'memo1': str(type_value),
                            'memo2': '',
                            'memo3': '',
                            'memo4': '',
                            'memo5': ''}
                )
            ]
        )
        logger.info(f"插入结果: {ret}")
    elif type_value == 1:
        # 连接集合
        collection = client.get('text_collection')
        logger.info("向文本向量库插入数据")
        ret = collection.insert(
            [
                Doc(
                    id=str(random_uuid),
                    vector=vectors,
                    fields={'title': file_name,
                            'url': '',
                            'value': input_value,
                            'id': '',
                            'memo1': str(type_value),
                            'memo2': '',
                            'memo3': '',
                            'memo4': '',
                            'memo5': ''}
                )
            ]
        )
        logger.info(f"插入结果: {ret}")
    logger.info("向量数据库存储成功")

def generate_embeddings(file_url, input_value, type_value):
    logger.info(f"生成向量 embeddings, type: {type_value}")
    if type_value == 0:
        input2 = [{'image': file_url}]
        logger.info("调用多模态embedding模型处理图片")

        # 调用模型接口
        resp = dashscope.MultiModalEmbedding.call(
            model="multimodal-embedding-v1",
            input=input2
        )
        if resp.status_code == HTTPStatus.OK:
            embeddings_list = resp.output['embeddings']
            first_embedding_dict = embeddings_list[0]
            logger.info("图片向量化成功")
            return first_embedding_dict['embedding']
        else:
            logger.error(f"图片向量化失败，状态码: {resp.status_code}")
            return []
    elif type_value == 1:
        client = OpenAI(
            # 若没有配置环境变量，请用百炼API Key将下行替换为：api_key="sk-xxx",
            api_key=dashscope_api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        )
        logger.info("调用文本embedding模型处理文本")
        # 调用模型接口
        resp = client.embeddings.create(
            model="text-embedding-v3",
            input=str(input_value),
            dimensions=1024
        )
        if resp.data:
            logger.info("文本向量化成功")
            return resp.data[0].embedding
        else:
            logger.error("文本向量化失败")
            return []

    return []

def generate_embeddings_v2(text_value, img_url):
    logger.info("生成多模态 embeddings")
    input2 = []
    if img_url:
        input2.append({'factor': 2, 'image': img_url})
    if text_value:
        input2.append({'factor': 1, 'text': text_value})

    logger.info("调用多模态embedding模型处理图文混合内容")
    # 调用模型接口
    resp = dashscope.MultiModalEmbedding.call(
        model="multimodal-embedding-v1",
        input=input2
    )

    if resp.status_code == HTTPStatus.OK:
        embeddings_list = resp.output['embeddings']
        first_embedding_dict = embeddings_list[0]
        logger.info("多模态向量化成功")
        return first_embedding_dict['embedding']
    else:
        logger.error(f"多模态向量化失败，状态码: {resp.status_code}")
        return []

# 提出问题
@app.route("/asking", methods=["POST"])
def asking():
    logger.info("收到提问请求")
    # 初始化 dashvector client
    dashscope.api_key = dashscope_api_key
    # 初始化 dashvector client
    client = Client(
        api_key=dashvector_api_key,
        endpoint=dashvector_endpoint
    )
    total_tokens = 0
    prompt_tokens = 0
    completion_tokens = 0
    # 解析传参
    param = request.json
    logger.info(f"提问参数: {param}")

    # 定义变量
    answer_back = {"fileName": '', "fileUrl": '', "answer": ''}

    if 'question' in param:
        user_id = param['question'].get('userId', '')
        knowledge_id = param['question'].get('knowledgeId', '')
        
        if 'imgUrl' in param['question']:
            img_url = param['question']['imgUrl']
        else:
            img_url = ''
        if 'questionValue' in param['question']:
            question_value = param['question']['questionValue']
        else:
            if img_url:
                question_value = '你好'
            else:
                question_value = ''
    else:
        user_id = ''
        knowledge_id = ''
        question_value = '你好'
        img_url = ''

    if 'type' in param:
        param_type = param['type']
    else:
        param_type = 1
    
    logger.info(f'提问题传参为，问题：{question_value} 图片地址：{img_url} 类型为：{param_type}')

    # 根据提问内容选择需要查找的向量数据库
    # 修改这一行
    collection_type_result = collection_type_question(question_value)
    collection_type = collection_type_result["answer"] if isinstance(collection_type_result, dict) else collection_type_result
    logger.info(f'需要查找的向量数据库：{collection_type}')
    # 如果是字典对象，则累加token统计
    if isinstance(collection_type_result, dict):
        total_tokens = total_tokens + collection_type_result["total_tokens"]
        prompt_tokens = prompt_tokens + collection_type_result["prompt_tokens"]
        completion_tokens = completion_tokens + collection_type_result["completion_tokens"]
    # 根据type传参来选用不同的模型和结果,type==1向量数据库、type==2LLM模型、type==3两者均有
    if param_type == 1:
        if collection_type == '图片':
            logger.info("查询图片向量库")
            # 连接集合
            collection = client.get(param['question']['collectionName'])
            # 向量检索：指定 topk = 1，返回文件名、图片地址、输入内容、类型（0-图片，1-文本）
            rsp = collection.query(generate_embeddings_v2(question_value, img_url), output_fields=['title', 'url', 'value', 'memo1'],
                                   topk=5)
            # 检查查询结果是否有效
            if rsp and rsp.output:
                answer_back['fileName'] = rsp.output[0].fields['title']
                answer_back['fileUrl'] = rsp.output[0].fields['url']
                answer_back['answer'] = rsp.output[0].fields['value']
                
                answer_back['collectionAnswer'] = rsp.output[0:5] # 取前5个结果返回给前端展示
            else:
                logger.error("图片向量库查询未返回有效结果")
                answer_back['answer'] = "抱歉，未找到相关图片结果"
        elif collection_type == '文本':
            logger.info("查询文本向量库")
            # 连接集合
            collection = client.get(param['question']['collectionName'])
            # 向量检索：指定 topk = 1，返回文件名、图片地址、输入内容、类型（0-图片，1-文本）
            rsp = collection.query(generate_embeddings('', question_value, 1), output_fields=['title', 'url', 'value', 'memo1'],
                                   topk=5)
            # 检查查询结果是否有效
            if rsp and rsp.output:
                answer_back['fileName'] = rsp.output[0].fields['title']
                answer_back['fileUrl'] = rsp.output[0].fields['url']
                answer_back['answer'] = rsp.output[0].fields['value']
                
                answer_back['collectionAnswer'] = rsp.output[0:5] # 取前5个结果返回给前端展示
            else:
                logger.error("文本向量库查询未返回有效结果")
                answer_back['answer'] = "抱歉，未找到相关文本结果"
        else:
            logger.info("查询多个向量库")
            # 连接集合
            collection = client.get(param['question']['collectionName'])
            # 向量检索：指定 topk = 1，返回文件名、图片地址、输入内容、类型（0-图片，1-文本）
            rsp = collection.query(generate_embeddings_v2(question_value, img_url), output_fields=['title', 'url', 'value', 'memo1'],
                                   topk=5)
            # 检查查询结果是否有效
            if rsp and rsp.output:
                answer_back['fileName'] = rsp.output[0].fields['title']
                answer_back['fileUrl'] = rsp.output[0].fields['url']
                answer_back['answer'] = rsp.output[0].fields['value']
                
                answer_back['collectionAnswer'] = rsp.output[0:5] # 取前5个结果返回给前端展示
            else:
                logger.error("多模态向量库查询未返回有效结果")
                answer_back['answer'] = "抱歉，未找到相关结果"
            
            # 连接集合
            #collection2 = client.get(param['question']['collectionName'])
            # 向量检索：指定 topk = 1，返回文件名、图片地址、输入内容、类型（0-图片，1-文本）
            #rsp2 = collection2.query(generate_embeddings('', question_value, 1), output_fields=['title', 'url', 'value', 'memo1'],
            #                       topk=5)
            # 检查第二个查询结果是否有效
            #if rsp2 and rsp2.output:
            #    answer_back['answer'] = rsp2.output[0].fields['value']
            #else:
            #    logger.error("第二个向量库查询未返回有效结果")
            
            # 保存查询记录到数据库
            record_id = insert_question_record(
                question_content=question_value,
                question_type=param_type,
                answer_content=answer_back['answer'],
                question_token=prompt_tokens,
                answer_token=completion_tokens,
                total_token=total_tokens,
                user_id=user_id,
                knowledge_id=knowledge_id,
                question_url=img_url
            )
            answer_back['recordId'] = record_id
            
            # 去base_sys_user表扣除消耗的token
            if user_id and total_tokens > 0:
                # 方式1: 只扣除总token
                deduct_result = deduct_user_token(user_id, total_tokens)
                if deduct_result:
                    logger.info(f"成功为用户 {user_id} 扣除 {total_tokens} 个token")
                    answer_back['token_deducted'] = True
                else:
                    logger.warning(f"为用户 {user_id} 扣除token失败")
                    answer_back['token_deducted'] = False
            
            logger.info(f"返回结果: {answer_back}")
            return answer_back
        
        # 保存查询记录到数据库
        record_id = insert_question_record(
            question_content=question_value,
            question_type=param_type,
            answer_content=answer_back['answer'],
            question_token=prompt_tokens,
            answer_token=completion_tokens,
            total_token=total_tokens,
            user_id=user_id,
            knowledge_id=knowledge_id,
            question_url=img_url
        )
        answer_back['recordId'] = record_id
        
        # 去base_sys_user表扣除消耗的token
        if user_id and total_tokens > 0:
            # 方式1: 只扣除总token
            deduct_result = deduct_user_token(user_id, total_tokens)
            if deduct_result:
                logger.info(f"成功为用户 {user_id} 扣除 {total_tokens} 个token")
                answer_back['token_deducted'] = True
            else:
                logger.warning(f"为用户 {user_id} 扣除token失败")
                answer_back['token_deducted'] = False
        
        logger.info(f"返回结果: {answer_back}")
        return answer_back

    elif param_type == 2:
        logger.info("使用LLM直接回答问题")
        if img_url:
            answer = llm_go(question_value, img_url)
        else:
            answer = deepseek_go(question_value)
        logger.info(f'type2问题的答案为: {answer["answer"]}')
        total_tokens = total_tokens + answer["total_tokens"]
        prompt_tokens = prompt_tokens + answer["prompt_tokens"]
        completion_tokens = completion_tokens + answer["completion_tokens"]
        answer_back['answer'] = answer["answer"]
        answer_back['total_tokens'] = total_tokens
        answer_back['prompt_tokens'] = prompt_tokens
        answer_back['completion_tokens'] = completion_tokens
        
        # 保存查询记录到数据库
        record_id = insert_question_record(
            question_content=question_value,
            question_type=param_type,
            answer_content=answer_back['answer'],
            question_token=prompt_tokens,
            answer_token=completion_tokens,
            total_token=total_tokens,
            user_id=user_id,
            knowledge_id=knowledge_id,
            question_url=img_url
        )
        answer_back['recordId'] = record_id
        
        # 去base_sys_user表扣除消耗的token
        if user_id and total_tokens > 0:
            # 方式1: 只扣除总token
            deduct_result = deduct_user_token(user_id, total_tokens)
            if deduct_result:
                logger.info(f"成功为用户 {user_id} 扣除 {total_tokens} 个token")
                answer_back['token_deducted'] = True
            else:
                logger.warning(f"为用户 {user_id} 扣除token失败")
                answer_back['token_deducted'] = False
        
        logger.info(f"返回结果: {answer_back}")
        return answer_back
    else:
        logger.info("使用向量检索+LLM组合方式回答问题")
        if collection_type == '图片':
            logger.info("处理图片类型问题")
            # 连接集合
            collection = client.get(param['question']['collectionName'])
            # 向量检索：指定 topk = 1，返回文件名、图片地址、输入内容、类型（0-图片，1-文本）
            rsp = collection.query(generate_embeddings_v2(question_value, img_url), output_fields=['title', 'url', 'value', 'memo1'],
                                   topk=5)
            question_type = 0
            prompt_value = ''
            # 检查查询结果是否有效
            if rsp and rsp.output:
                prompt_url = rsp.output[0].fields['url']
                answer_back['fileName'] = rsp.output[0].fields['title']
                answer_back['fileUrl'] = rsp.output[0].fields['url']
                answer_back['answer'] = rsp.output[0].fields['value']
                
                answer_back['collectionAnswer'] = rsp.output[0:5] # 取前5个结果返回给前端展示
            else:
                logger.error("图片向量库查询未返回有效结果")
                prompt_url = ''
                answer_back['answer'] = "抱歉，未找到相关图片结果"
        elif collection_type == '文本':
            logger.info("处理文本类型问题")
            # 连接集合
            collection = client.get(param['question']['collectionName'])
            # 向量检索：指定 topk = 1，返回文件名、图片地址、输入内容、类型（0-图片，1-文本）
            rsp = collection.query(generate_embeddings('', question_value, 1), output_fields=['title', 'url', 'value', 'memo1'],
                                   topk=5)
            question_type = 1
            # 检查查询结果是否有效
            if rsp and rsp.output:
                prompt_value = rsp.output[0].fields['value']
                answer_back['fileName'] = rsp.output[0].fields['title']
                answer_back['fileUrl'] = rsp.output[0].fields['url']
                answer_back['answer'] = rsp.output[0].fields['value']
                
                answer_back['collectionAnswer'] = rsp.output[0:5] # 取前5个结果返回给前端展示
                prompt_url = ''
            else:
                logger.error("文本向量库查询未返回有效结果")
                prompt_value = ''
                answer_back['answer'] = "抱歉，未找到相关文本结果"
                prompt_url = ''
        else:
            logger.info("处理混合类型问题")
            # 连接集合
            collection = client.get(param['question']['collectionName'])
            # 向量检索：指定 topk = 1，返回文件名、图片地址、输入内容、类型（0-图片，1-文本）
            rsp = collection.query(generate_embeddings_v2(question_value, img_url), output_fields=['title', 'url', 'value', 'memo1'],
                                   topk=5)
            # 检查查询结果是否有效
            if rsp and rsp.output:
                answer_back['fileName'] = rsp.output[0].fields['title']
                answer_back['fileUrl'] = rsp.output[0].fields['url']
                answer_back['answer'] = rsp.output[0].fields['value']
                
                answer_back['collectionAnswer'] = rsp.output[0:5] # 取前5个结果返回给前端展示
                prompt_value = rsp.output[0].fields['value']
                prompt_url = rsp.output[0].fields['url']
            else:
                logger.error("混合类型向量库查询未返回有效结果")
                answer_back['answer'] = "抱歉，未找到相关结果"
            
            # 连接集合
            #collection2 = client.get(param['question']['collectionName'])
            # 向量检索：指定 topk = 1，返回文件名、图片地址、输入内容、类型（0-图片，1-文本）
            #rsp2 = collection2.query(generate_embeddings('', question_value, 1), output_fields=['title', 'url', 'value', 'memo1'],
            #                       topk=1)
            #question_type = 1
            # 检查第二个查询结果是否有效
            #if rsp2 and rsp2.output:
            #    prompt_value = rsp2.output[0].fields['value']
            #else:
            #   logger.error("第二个向量库查询未返回有效结果")
            #    prompt_value = ''
            
        # 如果向量检索没有返回有效结果，则使用LLM直接回答
        if not answer_back['answer']:
            answer = answer_question(question_type, question_value, prompt_value, prompt_url)
            logger.info(f'type3问题的答案为: {answer["answer"]}')

            total_tokens = total_tokens + answer["total_tokens"]
            prompt_tokens = prompt_tokens + answer["prompt_tokens"]
            completion_tokens = completion_tokens + answer["completion_tokens"]
            answer_back['answer'] = answer["answer"]
        else:
            # 如果向量检索返回了结果，则使用该结果
            answer = {
                "answer": answer_back['answer'],
                "total_tokens": 0,
                "prompt_tokens": 0,
                "completion_tokens": 0
            }

        # 保存查询记录到数据库
        record_id = insert_question_record(
            question_content=question_value,
            question_type=param_type,
            answer_content=answer_back['answer'],
            question_token=prompt_tokens,
            answer_token=completion_tokens,
            total_token=total_tokens,
            user_id=user_id,
            knowledge_id=knowledge_id,
            question_url=img_url
        )
        answer_back['recordId'] = record_id
        
        # 去base_sys_user表扣除消耗的token
        if user_id and total_tokens > 0:
            # 方式1: 只扣除总token
            deduct_result = deduct_user_token(user_id, total_tokens)
            if deduct_result:
                logger.info(f"成功为用户 {user_id} 扣除 {total_tokens} 个token")
                answer_back['token_deducted'] = True
            else:
                logger.warning(f"为用户 {user_id} 扣除token失败")
                answer_back['token_deducted'] = False
        
        logger.info(f"返回结果: {answer_back}")
        return answer_back

# 根据提问内容选择需要查找的向量数据库
def collection_type_question(question_value):
    logger.info(f"判断问题类型: {question_value}")
    if question_value:
        prompt = f'''我拥有两个向量数据库，一个是图片数据库，一个是文本数据库，请根据用户提问内容，判断用户希望查找图片还是希望查找文本，请只返回“图片”或者“文本”或者“两种都有”。
            用户问题：{question_value}
            '''
        result = deepseek_go(prompt)
        logger.info(f"问题类型判断结果: {result}")
        return result

# 构造 Prompt
def answer_question(question_type, question_value, prompt_value, img_url):
    logger.info(f"构建回答问题的Prompt: type={question_type}, question={question_value}")
    if question_type == 1:
        if question_value:
            prompt = f'''请基于```内的内容回答问题。"
                ```
                {prompt_value}
                ```
                我的问题是：{question_value}。
                '''
            return llm_go(prompt, '')
        else:
            return prompt_value
    else:
        prompt = '请描述图片内容'
        return llm_go(prompt, img_url)

# GPT方法,向LLM提问
def llm_go(question_value, img_url):
    logger.info(f"调用Qwen模型回答问题: {question_value}")
    client = OpenAI(
        # 若没有配置环境变量，请用百炼API Key将下行替换为：api_key="sk-xxx",
        api_key=dashscope_api_key,
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    )
    content_value = []
    if question_value:
        text_value = {"type": "text", "text": question_value}
        content_value.append(text_value)
    if img_url:
        img_value = {"type": "image_url", "image_url": img_url}
        content_value.append(img_value)
    
    logger.info("开始调用Qwen模型")
    # 调用llm模型
    completion = client.chat.completions.create(
        model="qwen-omni-turbo",
        messages=[
            {
                "role": "system",
                "content": [{"type": "text", "text": "You are a helpful assistant."}],
            },
            {
                "role": "user",
                "content": content_value,
            },
        ],
        # 设置输出数据的模态，当前支持两种：["text","audio"]、["text"]
        modalities=["text"],
        # stream 必须设置为 True，否则会报错
        stream=True,
        stream_options={
            "include_usage": True
        }
    )
    logger.info("Qwen模型调用成功，开始接收流式响应")
    answer = ''
    total_tokens = 0
    prompt_tokens = 0
    completion_tokens = 0
    
    for chunk in completion:
        if chunk.choices:
            if chunk.choices[0].delta.content:
                answer = answer + chunk.choices[0].delta.content
        elif chunk.usage:
            # 记录token消耗量
            total_tokens = chunk.usage.total_tokens
            prompt_tokens = chunk.usage.prompt_tokens
            completion_tokens = chunk.usage.completion_tokens
            logger.info(f"Qwen模型Token消耗 - 总计: {total_tokens}, 提示: {prompt_tokens}, 完成: {completion_tokens}")

    logger.info(f"Qwen模型回答完成，总token消耗: {total_tokens}")
    data = {"answer": answer, "total_tokens": total_tokens, "prompt_tokens": prompt_tokens, "completion_tokens": completion_tokens}
    return data

# GPT方法,向deepseek提问
def deepseek_go(question_value):
    logger.info(f"调用DeepSeek模型回答问题: {question_value}")
    client = OpenAI(
        # 若没有配置环境变量，请用百炼API Key将下行替换为：api_key="sk-xxx",
        api_key=dashscope_api_key,
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    )
    
    logger.info("开始调用DeepSeek模型")
    # 调用llm模型
    completion = client.chat.completions.create(
        model="deepseek-v3",
        messages=[
            {
                "role": "user",
                "content": question_value
            }
        ]
    )

    logger.info("DeepSeek模型调用成功")
    answer = completion.choices[0].message.content
    
    total_tokens = 0
    prompt_tokens = 0
    completion_tokens = 0
    # 输出token消耗量
    if hasattr(completion, 'usage') and completion.usage:
        total_tokens = completion.usage.total_tokens
        prompt_tokens = completion.usage.prompt_tokens
        completion_tokens = completion.usage.completion_tokens
        logger.info(f"DeepSeek模型Token消耗 - 总计: {total_tokens}, 提示: {prompt_tokens}, 完成: {completion_tokens}")
    
    logger.info(f"DeepSeek模型回答完成")
    data = {"answer": answer, "total_tokens": total_tokens, "prompt_tokens": prompt_tokens, "completion_tokens": completion_tokens}
    return data

if __name__ == '__main__':
    logger.info("应用启动中...")
    pool = create_conn_pool()
    logger.info("应用启动完成，监听端口 18052")
    app.run(host="0.0.0.0", port=18052)